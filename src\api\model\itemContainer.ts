import { ItemDefinitions } from '../../data/itemDefinitions'
import { Bank } from '../game/bank'
import { Equipment } from '../game/equipment'
import { Inventory } from '../game/inventory'
import { sum } from '../utils/arrayExtensions'
import { log } from '../utils/utils'
import { Item } from './item'

export class ItemContainer {
    hasThisItemNoted(itemPredicateAtInventory: (item: Item) => boolean): boolean {
        
        const matchingDefinitions = ItemDefinitions.definitions.filter(def => {
            const testItem = new Item(def.id, 1)
            return itemPredicateAtInventory(testItem)
        })

        for (const def of matchingDefinitions) {
            if(this.countNotedOnly(def.id) > 0) return true
        }

        return false
    }

    static allCombined() {
        let container = Inventory.get()
        const eq = Equipment.get()

        if (Bank.cache) container = container.and(Bank.cache)
        if (eq) container = container.and(eq)

        return container
    }

    getAllExceptIds(...exceptIds: number[]) {
        return this.getAllByPredicate((i) => !exceptIds.includes(i.id))
    }
    getAllByPredicate(predicate: (i: any) => boolean) {
        return this.items.filter(predicate)
    }

    orderItemsByName(ascending: boolean = false): ItemContainer {
        this.items = this.items.sort((a, b) => {
            if (a.definition.name < b.definition.name) {
                return -1
            }
            if (a.definition.name > b.definition.name) {
                return 1
            }
            return 0
        })

        if (ascending) {
            this.items = this.items.reverse()
        }
        return this
    }

    orderItemsById(): ItemContainer {
        this.items = this.items.sort((a, b) => a.id - b.id)
        return this
    }
    reverse(): ItemContainer {
        this.items.reverse()
        return this
    }

    getAny(...itemIds: number[]) {
        return this.getByPredicate((item) => itemIds.some((id) => id == item.id))
    }

    containsAllById(...ids: number[]) {
        return ids.every((id) => this.contains(id, 1))
    }

    containsByPredicate(itemPredicate: (item: Item) => boolean): boolean {
        return this.countByPredicate(itemPredicate) > 0
    }

    countByIds(...itemIds: number[]): number {
        return this.countByPredicate((item) => itemIds.some((id) => id == item.id))
    }

    where(predicate: (item: Item) => boolean) {
        return this.items.filter(predicate)
    }

    getByPredicate(predicate: (item: Item) => boolean) {
        return this.items.find(predicate)
    }

    containsAny(...itemIds: number[]) {
        return this.containsByPredicate((item) => itemIds.includes(item.id))
    }

    constructor(items: Array<Item>) {
        this.items = items
    }

    items: Array<Item>

    byId = (id: number) => this.items.find((i) => i.id == id)
    byNameStartsWith = (startsWith: string) => this.items.find((i) => i.definition.name.toLowerCase().startsWith(startsWith.toLowerCase()))

    getCount = (...ids: number[]): number => {
        return sum(this.items.filter((i) => ids.includes(i.id)).map((i) => i.amount))
    }

    countByPredicate = (predicate: (item: Item) => boolean) => {
        return sum(this.items.filter(predicate).map((i) => i.amount))
    }

    contains = (id: number, amount?: number) => this.getCount(id) >= (amount ?? 1)

    and(container: ItemContainer) {
        const items = JSON.parse(JSON.stringify(this.items))
        container?.items?.forEach((item) => items.push(item))
        return new ItemContainer(items)
    }

    countWithNoted(itemId: number): number {
        const def = ItemDefinitions.getById(itemId)

        if (def.isStackable && !def.isNoted) {
            return this.getCount(itemId)
        }

        if (def.isStackable && def.isNoted) {
            return this.getCount(def.unnotedId) + this.countByPredicate((i) => true)
        }

        if (itemId != def.realNotedId) {
            return this.getCount(itemId) + this.getCount(def.realNotedId)
        }

        return this.getCount(itemId)
    }

    countNotedOnly(itemId: number): number {
        const def = ItemDefinitions.getById(itemId)

        if (def.isNoted) {
            return this.getCount(itemId)
        }

        return this.getCount(def.realNotedId)
    }
    
}
