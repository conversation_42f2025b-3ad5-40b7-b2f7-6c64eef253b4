import { LoginEvent } from '../../api/core/script/game-random-events/loginEvent'
import { StatefulScript } from '../../api/core/script/statefulScript'
import { Skill } from '../../api/game/skill'
import { Item } from '../../api/model/item'
import { TradePackage } from '../../api/model/tradePackage'
import { GiveToMuleState } from '../../api/script-utils/mule/giveMuleStrategy'
import { ResupplyMuleState } from '../../api/script-utils/mule/resupplyMuleStrategy'
import { hourRatio } from '../../api/utils/utils'
import { MuleReceiver } from '../muling/muleReceiver'
import { BfData } from './bfConstants'
import { MakeIronBarStrategy } from './states/bfIron'

export class BlastFurnace extends StatefulScript {
    static main = new MakeIronBarStrategy()

    static resupply = new ResupplyMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_RESUPPLY_2m), 358)

    static giveToMule = new GiveToMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_GIVE), 358, [new Item(995, 500_000)], [new Item(995, ***********)])

    constructor() {
        super()
        LoginEvent.forceWorld = 358
    }

    onStart(): void {
        this.loopInterval = 300
        this.initWithState(BlastFurnace.main)
    }

    onDraw(canvas: any, paint: any): void {
        this.drawText('Blast Furnace')
        this.drawText('State: ' + this.currentState.name)
        this.drawText('steelBars: ' + BfData.steelBars)
        this.drawText('isCoalBagFilled: ' + BfData.isCoalBagFilled)
    }

    getExpGained() {
        return this.progressTracker.getExpGained(Skill.SMITHING)
    }

    getItemsMade() {
        return Math.round(this.getExpGained() / 17.5)
    }

    runtimeInfo(): string {
        return `<th>State: ${this.currentState.name}</th><th>Bars: ${this.getItemsMade()} (${hourRatio(this.progressTracker.startTime, this.getItemsMade())}}</th><th>SM: ${Skill.SMITHING.getCurrentLevel()}</th>`
    }
}
