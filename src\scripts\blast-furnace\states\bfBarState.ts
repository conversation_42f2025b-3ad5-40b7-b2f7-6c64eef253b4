import { State } from '../../../api/core/script/state'
import { Bank, Withdraw } from '../../../api/game/bank'
import { Dialogue } from '../../../api/game/dialogue'
import { GameObjects } from '../../../api/game/gameObjects'
import { InputBox } from '../../../api/game/inputBox'
import { Inventory } from '../../../api/game/inventory'
import { Walking } from '../../../api/game/walking'
import { Widgets } from '../../../api/game/widgets'
import { Item } from '../../../api/model/item'
import { MenuOpcode } from '../../../api/model/menuOpcode'
import { Tile } from '../../../api/model/tile'
import { Time } from '../../../api/utils/time'
import { log } from '../../../api/utils/utils'
import { Player } from '../../../api/wrappers/player'
import { ItemId } from '../../../data/itemId'
import { ItemPredicate } from '../../../data/itemPredicates'
import { Locations } from '../../../data/locations'
import { BfData } from '../bfConstants'
import { BlastFurnace } from '../blastFurnace'

export class BfBarState extends State {
    COAL = 453
    COAL_BAG = 12019
    IRON_ORE = 440
    ADAMANT_ORE = 449
    CONVEYOR_BELT = 9100
    BAR_DISPENDER = 9092
    FOREMAN = 2923
    coalBagFilled = false
    oreToGrab = this.ADAMANT_ORE

    onAction(): void {}

    // protected withdraw(item: number, sleep: boolean = true): void {
    //     if (!Inventory.get().contains(item, 27)) {
    //         if (Bank.open()) {
    //             if (Inventory.getFreeSlots() < 27) {
    //                 Bank.depositAllExcept([this.COAL_BAG])
    //                 Time.sleep(1300) //TODO Dynamic sleep
    //             }
    //             if (!Bank.get().contains(item)) {
    //                 this.setState(BlastFurnace.ge)
    //                 return
    //             }
    //             if (Inventory.getFreeSlots() < 27) {
    //                 return
    //             }

    //             Withdraw.builder().id(item).amount(28).withdrawAll().minimumAmount(1).withdraw()
    //             // if (sleep) Time.sleep(3000, 10, () => Inventory.get().contains(item, 27));
    //         }
    //     }
    // }

    protected grabBars(): void {
        log('GRAB BARS')
        const tile = new Tile(1940, 4962, 0)

        if (Widgets.get(********) != null) {
            Widgets.get(********).click(57, 1)
            if (Time.sleep(2000, 30, () => Inventory.get().contains(ItemId.STEEL_BAR))) {
                Walking.walkTo(new Tile(1948, 4957, 0), 1)
            }
            return
        }

        if (tile.distance() <= 3 || BfData.steelBars > 0) {
            if (Inventory.getFreeSlots() < 10) {
                if (Bank.openNearest()) {
                    Bank.depositAllExcept([this.COAL_BAG])
                    return
                }
            }
            if (Widgets.get(********) == null) {
                GameObjects.getById(this.BAR_DISPENDER).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
                Time.sleep(1200, 10, () => Widgets.get(********) != null)
            }

            if (Widgets.get(********) != null) {
                const widget = Widgets.get(********)

                //TODO FIX THIS SHIT
                widget.click(57, 1)

                Time.sleep(2000, 30, () => Inventory.get().contains(ItemId.STEEL_BAR))
                Walking.walkTo(new Tile(1948, 4957, 0), 1)
            }
        } else {
            Walking.walkTo(tile, 1)
            Time.sleep(400, 600)
        }
    }

    protected ensureCoffer(): boolean {
        if (Locations.bankBlastFurnace.distance() > 35) {
            return true
        }
        if (BfData.coinsInCoffer > 1000) {
            return true
        }

        if(!Withdraw.all(null, Withdraw.id(995, 75000, 75000).ensureSpace())) {
            return false
        }

        Inventory.get().byId(995).click(25, 0) //use
        Time.sleep(300, 800)
        GameObjects.getById(29330).click(MenuOpcode.ITEM_USE_ON_OBJECT)

        if (Time.sleep(() => InputBox.isOpen)) {
            InputBox.type('100000', 100, true)
            Time.sleep(() => BfData.coinsInCoffer > 2000)
        }
        return false
    }

    protected walkBlastFurnace(): boolean {
        if (Locations.bankBlastFurnace.distance() > 20) {
            if (Dialogue.isOpen()) {
                Dialogue.goNext('Yes please')
                return false
            }
            log('Walking to blast furnace')
            Walking.walkTo(Locations.bankBlastFurnace)
            return false
        }
        return true
    }

    protected shouldDrinkStamina(): boolean {
        return !Player.isStaminaPotionActive() || Player.getRunEnergy() < 15
    }

    protected drinkStaminaPotion(geState: State): boolean {
        if (Player.isStaminaPotionActive() && Player.getRunEnergy() > 50) {
            return true
        }

        if(!Withdraw.all(geState, Withdraw.predicate(ItemPredicate.staminaPotion, 1).ensureSpace())) {
            return
        }

        if (Bank.open()) {
            Inventory.get(Inventory.bank).getByPredicate(ItemPredicate.staminaPotion).click(1007, 9)
            return  Time.sleep(1000, 30, () => Player.isStaminaPotionActive())
        }

        return false
    }

    protected putOre(): void {
        log('put ore in pachore')
        GameObjects.getById(this.CONVEYOR_BELT).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
        Time.sleep(3000, () => Inventory.getFreeSlots() > 20)
    }

    protected emptyCoalBag(): void {
        Widgets.closeTopInterface()

        Inventory.get().byId(this.COAL_BAG).click(1007, 6)
        log('dyn 2')
        if (Time.sleep(() => Inventory.get().contains(this.COAL))) {
            this.coalBagFilled = false
        }
    }

    // protected fillCoalBag(): void {
    //     if (BfData.isCoalBagFilled) {
    //         this.coalBagFilled = true
    //         return
    //     }
    //     if (Bank.open()) {
    //         if (Bank.get().getCount(this.COAL) >= 27) {
    //             this.clickFillCoalBag()
    //             this.coalBagFilled = Time.sleep(4000, 10, () => BfData.isCoalBagFilled)
    //         } else {
    //             this.setState(BlastFurnace.ge)
    //         }
    //     }
    // }

    protected clickFillCoalBag(): void {
        if (Bank.isOpen() && !BfData.isCoalBagFilled) {
            const bag = Inventory.get(Inventory.bank).byId(this.COAL_BAG)
            bag.click(1007, 9)
        }
    }

    protected depositPredicate(...items: number[]): (item: Item) => boolean {
        const list = new Set(items)
        return (item: Item) => list.has(item.id)
    }
}
